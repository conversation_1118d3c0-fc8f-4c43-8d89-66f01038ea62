import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Info, Volume2, VolumeX } from 'lucide-react';

const QuestionContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  margin: 1rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 180px;
  position: relative;
  box-sizing: border-box;

  /* 性能优化 */
  will-change: transform;
  transform: translateZ(0);

  @media (min-width: 768px) {
    padding: 2rem;
    margin: 2rem 0;
    min-height: 200px;
  }
`;

const DetailButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  &:hover {
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
`;

const AudioButton = styled.button`
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  &:hover {
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }

  &.playing {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.7);
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
  }
`;

const QuestionText = styled.h2`
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.4;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  
  @media (max-width: 768px) {
    font-size: 1.4rem;
  }
  
  @media (max-width: 480px) {
    font-size: 1.2rem;
  }
`;

const QuestionLabel = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const PronunciationContainer = styled.div`
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
`;

const PronunciationText = styled.div`
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const ChinesePronunciationText = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-style: italic;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const QuestionArea = ({
  sentence,
  pronunciation,
  chinesePronunciation,
  isLoading = false,
  onDetailClick,
  sentenceId,
  autoPlay = false
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [speechSupported, setSpeechSupported] = useState(true);

  useEffect(() => {
    // 检查浏览器是否支持语音合成
    setSpeechSupported('speechSynthesis' in window);
  }, []);

  useEffect(() => {
    // 自动播放功能
    if (autoPlay && sentence && speechSupported && !isLoading) {
      const timer = setTimeout(() => {
        playPronunciation();
      }, 500); // 延迟500ms播放

      return () => clearTimeout(timer);
    }
  }, [sentence, autoPlay, speechSupported, isLoading]);

  const playPronunciation = () => {
    if (!speechSupported || !sentence || isPlaying) return;

    try {
      // 停止当前播放的语音
      window.speechSynthesis.cancel();

      setIsPlaying(true);

      const utterance = new SpeechSynthesisUtterance(sentence);
      utterance.lang = 'en-US';
      utterance.rate = 0.8; // 稍微慢一点的语速
      utterance.pitch = 1;
      utterance.volume = 0.8;

      utterance.onend = () => {
        setIsPlaying(false);
      };

      utterance.onerror = () => {
        setIsPlaying(false);
        console.log('Speech synthesis error');
      };

      window.speechSynthesis.speak(utterance);
    } catch (error) {
      setIsPlaying(false);
      console.log('Speech synthesis not supported or error:', error);
    }
  };

  const stopPronunciation = () => {
    window.speechSynthesis.cancel();
    setIsPlaying(false);
  };

  return (
    <QuestionContainer>
      {speechSupported && sentence && !isLoading && (
        <AudioButton
          onClick={isPlaying ? stopPronunciation : playPronunciation}
          className={isPlaying ? 'playing' : ''}
          title={isPlaying ? '停止播放' : '播放发音'}
        >
          {isPlaying ? <VolumeX size={16} /> : <Volume2 size={16} />}
          <span>{isPlaying ? '停止' : '发音'}</span>
        </AudioButton>
      )}

      {onDetailClick && sentenceId && (
        <DetailButton
          onClick={() => onDetailClick(sentenceId)}
          title="查看句子详情"
        >
          <Info size={16} />
          <span>解释</span>
        </DetailButton>
      )}

      <QuestionLabel>English Sentence</QuestionLabel>
      <QuestionText>
        {isLoading ? 'Loading...' : sentence || 'No sentence available'}
      </QuestionText>

      {!isLoading && (pronunciation || chinesePronunciation) && (
        <PronunciationContainer>
          {pronunciation && (
            <PronunciationText>
              {pronunciation}
            </PronunciationText>
          )}
          {chinesePronunciation && (
            <ChinesePronunciationText>
              {chinesePronunciation}
            </ChinesePronunciationText>
          )}
        </PronunciationContainer>
      )}
    </QuestionContainer>
  );
};

export default QuestionArea;
