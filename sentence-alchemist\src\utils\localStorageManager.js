/**
 * 本地存储管理器
 * 为体验模式提供完整的本地数据存储功能
 */

const STORAGE_KEYS = {
  USER_PROFILE: 'sentence-alchemist-local-profile',
  LEARNING_DATA: 'sentence-alchemist-local-learning',
  SENTENCES_DATA: 'sentence-alchemist-local-sentences',
  PREFERENCES: 'sentence-alchemist-local-preferences',
  STATISTICS: 'sentence-alchemist-local-statistics',
  ACHIEVEMENTS: 'sentence-alchemist-local-achievements'
};

export class LocalStorageManager {
  constructor() {
    this.isExperienceMode = false;
    this.initializeExperienceMode();
  }

  /**
   * 初始化体验模式
   */
  initializeExperienceMode() {
    this.isExperienceMode = true;
    console.log('本地存储管理器：体验模式已启用');
  }

  /**
   * 检查是否为体验模式
   */
  isInExperienceMode() {
    return this.isExperienceMode;
  }

  /**
   * 保存数据到本地存储
   */
  saveData(key, data) {
    try {
      const serializedData = JSON.stringify({
        data,
        timestamp: Date.now(),
        version: '1.0'
      });
      localStorage.setItem(key, serializedData);
      return true;
    } catch (error) {
      console.error('保存本地数据失败:', error);
      return false;
    }
  }

  /**
   * 从本地存储读取数据
   */
  loadData(key, defaultValue = null) {
    try {
      const stored = localStorage.getItem(key);
      if (!stored) return defaultValue;

      const parsed = JSON.parse(stored);
      return parsed.data || defaultValue;
    } catch (error) {
      console.error('读取本地数据失败:', error);
      return defaultValue;
    }
  }

  /**
   * 删除本地存储数据
   */
  removeData(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('删除本地数据失败:', error);
      return false;
    }
  }

  /**
   * 清除所有本地数据
   */
  clearAllData() {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      console.log('所有本地数据已清除');
      return true;
    } catch (error) {
      console.error('清除本地数据失败:', error);
      return false;
    }
  }

  // ==================== 用户档案相关 ====================

  /**
   * 保存用户档案
   */
  saveUserProfile(profile) {
    return this.saveData(STORAGE_KEYS.USER_PROFILE, profile);
  }

  /**
   * 加载用户档案
   */
  loadUserProfile() {
    return this.loadData(STORAGE_KEYS.USER_PROFILE, {
      totalQuestionsAnswered: 0,
      totalCorrectAnswers: 0,
      averageAccuracy: 0,
      recentAccuracy: 0,
      preferredScenes: [],
      currentLevel: 'beginner',
      achievements: [],
      studyStreak: 0,
      lastStudyDate: null,
      experienceMode: true,
      createdAt: Date.now()
    });
  }

  // ==================== 学习数据相关 ====================

  /**
   * 保存句子学习数据
   */
  saveSentenceLearningData(sentenceId, learningData) {
    const allData = this.loadData(STORAGE_KEYS.LEARNING_DATA, {});
    allData[sentenceId] = {
      ...learningData,
      lastUpdated: Date.now()
    };
    return this.saveData(STORAGE_KEYS.LEARNING_DATA, allData);
  }

  /**
   * 加载句子学习数据
   */
  loadSentenceLearningData(sentenceId) {
    const allData = this.loadData(STORAGE_KEYS.LEARNING_DATA, {});
    return allData[sentenceId] || null;
  }

  /**
   * 加载所有学习数据
   */
  loadAllLearningData() {
    return this.loadData(STORAGE_KEYS.LEARNING_DATA, {});
  }

  // ==================== 用户偏好相关 ====================

  /**
   * 保存用户偏好
   */
  saveUserPreference(key, value) {
    const preferences = this.loadData(STORAGE_KEYS.PREFERENCES, {});
    preferences[key] = value;
    return this.saveData(STORAGE_KEYS.PREFERENCES, preferences);
  }

  /**
   * 加载用户偏好
   */
  loadUserPreference(key, defaultValue = null) {
    const preferences = this.loadData(STORAGE_KEYS.PREFERENCES, {});
    return preferences[key] !== undefined ? preferences[key] : defaultValue;
  }

  /**
   * 加载所有用户偏好
   */
  loadAllUserPreferences() {
    return this.loadData(STORAGE_KEYS.PREFERENCES, {});
  }

  // ==================== 学习统计相关 ====================

  /**
   * 保存学习统计
   */
  saveLearningStatistics(stats) {
    return this.saveData(STORAGE_KEYS.STATISTICS, stats);
  }

  /**
   * 加载学习统计
   */
  loadLearningStatistics() {
    return this.loadData(STORAGE_KEYS.STATISTICS, {
      totalSessions: 0,
      totalQuestions: 0,
      totalCorrect: 0,
      averageAccuracy: 0,
      totalStudyTime: 0,
      dailyStats: {},
      weeklyStats: {},
      monthlyStats: {}
    });
  }

  // ==================== 成就系统相关 ====================

  /**
   * 保存成就数据
   */
  saveAchievements(achievements) {
    return this.saveData(STORAGE_KEYS.ACHIEVEMENTS, achievements);
  }

  /**
   * 加载成就数据
   */
  loadAchievements() {
    return this.loadData(STORAGE_KEYS.ACHIEVEMENTS, []);
  }

  // ==================== 数据迁移相关 ====================

  /**
   * 导出所有本地数据
   */
  exportAllData() {
    const exportData = {
      userProfile: this.loadUserProfile(),
      learningData: this.loadAllLearningData(),
      preferences: this.loadAllUserPreferences(),
      statistics: this.loadLearningStatistics(),
      achievements: this.loadAchievements(),
      exportedAt: Date.now(),
      version: '1.0'
    };

    return exportData;
  }

  /**
   * 导入数据到本地存储
   */
  importData(importData) {
    try {
      if (importData.userProfile) {
        this.saveUserProfile(importData.userProfile);
      }
      if (importData.learningData) {
        this.saveData(STORAGE_KEYS.LEARNING_DATA, importData.learningData);
      }
      if (importData.preferences) {
        this.saveData(STORAGE_KEYS.PREFERENCES, importData.preferences);
      }
      if (importData.statistics) {
        this.saveLearningStatistics(importData.statistics);
      }
      if (importData.achievements) {
        this.saveAchievements(importData.achievements);
      }

      console.log('数据导入成功');
      return true;
    } catch (error) {
      console.error('数据导入失败:', error);
      return false;
    }
  }

  /**
   * 获取存储使用情况
   */
  getStorageUsage() {
    let totalSize = 0;
    const usage = {};

    Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
      const data = localStorage.getItem(key);
      const size = data ? new Blob([data]).size : 0;
      usage[name] = {
        key,
        size,
        sizeFormatted: this.formatBytes(size)
      };
      totalSize += size;
    });

    return {
      total: totalSize,
      totalFormatted: this.formatBytes(totalSize),
      breakdown: usage
    };
  }

  /**
   * 格式化字节大小
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 导出单例实例
export const localStorageManager = new LocalStorageManager();

// 导出存储键常量
export { STORAGE_KEYS };
