import React, { useState } from 'react';
import styled from 'styled-components';
import { Play, UserPlus, LogIn, Sparkles, BookOpen, Trophy, TrendingUp } from 'lucide-react';
import AuthModal from './AuthModal';

const WelcomeContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }
`;

const ContentWrapper = styled.div`
  display: flex;
  width: 100%;
  max-width: 1200px;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }
`;

const LeftSection = styled.div`
  flex: 1;
  color: white;
  
  @media (max-width: 768px) {
    order: 2;
  }
`;

const RightSection = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  
  @media (max-width: 768px) {
    order: 1;
    width: 100%;
  }
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #ffffff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.25rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
`;

const FeatureList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const FeatureItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  opacity: 0.9;

  svg {
    color: #ffd700;
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const ActionPanel = styled.div`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 400px;

  @media (max-width: 480px) {
    padding: 2rem;
    border-radius: 16px;
  }
`;

const PanelTitle = styled.h2`
  color: #2d3748;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-align: center;
`;

const PanelSubtitle = styled.p`
  color: #666;
  font-size: 1rem;
  margin: 0 0 2rem 0;
  text-align: center;
  line-height: 1.5;
`;

const ActionButton = styled.button`
  width: 100%;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    flex-shrink: 0;
  }
`;

const PrimaryButton = styled(ActionButton)`
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
  }
`;

const SecondaryButton = styled(ActionButton)`
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  
  &:hover {
    background: #667eea;
    color: white;
  }
`;

const ExperienceButton = styled(ActionButton)`
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
  }
`;

const ExperienceNote = styled.div`
  background: rgba(72, 187, 120, 0.1);
  border: 1px solid rgba(72, 187, 120, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: #2f855a;
  text-align: center;
  line-height: 1.4;
`;

const WelcomePage = ({ onExperienceMode, onAuthSuccess }) => {
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState('login'); // 'login' or 'register'

  const handleLoginClick = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const handleRegisterClick = () => {
    setAuthMode('register');
    setShowAuthModal(true);
  };

  const handleExperienceClick = () => {
    onExperienceMode?.();
  };

  const handleAuthModalSuccess = (user) => {
    setShowAuthModal(false);
    onAuthSuccess?.(user);
  };

  return (
    <WelcomeContainer>
      <ContentWrapper>
        <LeftSection>
          <Title>句之炼金</Title>
          <Subtitle>
            通过智能句子匹配，掌握真正的语言能力。
            让每一个句子都成为您语言学习路上的黄金。
          </Subtitle>
          
          <FeatureList>
            <FeatureItem>
              <Sparkles size={20} />
              <span>智能推荐系统，个性化学习路径</span>
            </FeatureItem>
            <FeatureItem>
              <BookOpen size={20} />
              <span>丰富场景句库，实用性极强</span>
            </FeatureItem>
            <FeatureItem>
              <Trophy size={20} />
              <span>成就徽章激励，让学习更有趣</span>
            </FeatureItem>
            <FeatureItem>
              <TrendingUp size={20} />
              <span>学习数据分析，掌握进步轨迹</span>
            </FeatureItem>
          </FeatureList>
        </LeftSection>

        <RightSection>
          <ActionPanel>
            <PanelTitle>开始您的学习之旅</PanelTitle>
            <PanelSubtitle>
              选择最适合您的方式开始使用句之炼金
            </PanelSubtitle>

            <ExperienceButton onClick={handleExperienceClick}>
              <Play size={20} />
              立即体验
            </ExperienceButton>
            
            <ExperienceNote>
              💡 无需注册，立即开始学习！所有数据保存在本地，随时可以升级到完整版本。
            </ExperienceNote>

            <SecondaryButton onClick={handleLoginClick}>
              <LogIn size={20} />
              登录账户
            </SecondaryButton>

            <PrimaryButton onClick={handleRegisterClick}>
              <UserPlus size={20} />
              注册新账户
            </PrimaryButton>
          </ActionPanel>
        </RightSection>
      </ContentWrapper>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onAuthSuccess={handleAuthModalSuccess}
        initialTab={authMode}
      />
    </WelcomeContainer>
  );
};

export default WelcomePage;
