import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  Calendar as CalendarIcon, 
  ChevronLeft, 
  ChevronRight,
  Target,
  TrendingUp,
  Award,
  Flame
} from 'lucide-react';
import { learningTracker } from '../utils/learningTracker';

const PageContainer = styled.div`
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  width: 100%;
  box-sizing: border-box;

  @media (min-width: 768px) {
    padding: 2rem;
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const Subtitle = styled.p`
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0;
`;

const CalendarContainer = styled.div`
  background: white;
  border-radius: 16px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  box-sizing: border-box;

  @media (min-width: 768px) {
    padding: 2rem;
  }
`;

const CalendarHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: 2rem;
`;

const MonthNavigation = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const NavButton = styled.button`
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
  }
`;

const MonthTitle = styled.h2`
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  min-width: 200px;
  text-align: center;
`;

const CalendarGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
`;

const DayHeader = styled.div`
  background: #f7fafc;
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
`;

const DayCell = styled.div`
  background: white;
  min-height: 80px;
  padding: 0.5rem;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f7fafc;
  }

  ${props => props.isToday && `
    background: #e6fffa;
    border: 2px solid #38b2ac;
  `}

  ${props => props.isOtherMonth && `
    background: #f7fafc;
    color: #a0aec0;
  `}
`;

const DayNumber = styled.div`
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
`;

const ActivityIndicator = styled.div`
  position: absolute;
  bottom: 0.25rem;
  right: 0.25rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => {
    if (props.count === 0) return 'transparent';
    if (props.count < 5) return '#c6f6d5';
    if (props.count < 10) return '#9ae6b4';
    if (props.count < 20) return '#68d391';
    return '#38a169';
  }};
`;

const ActivityCount = styled.div`
  font-size: 0.7rem;
  color: #718096;
  font-weight: 500;
`;

const StatsSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-left: 4px solid ${props => props.color || '#667eea'};
`;

const StatHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const StatIcon = styled.div`
  color: ${props => props.color || '#667eea'};
`;

const StatTitle = styled.h3`
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #718096;
`;

const CalendarPage = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarData, setCalendarData] = useState(new Map());
  const [monthStats, setMonthStats] = useState(null);

  useEffect(() => {
    loadCalendarData();
  }, [currentDate]);

  const loadCalendarData = () => {
    // 获取当月的学习数据
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // 获取学习统计数据
    const daysInMonth = lastDay.getDate() - firstDay.getDate() + 1;
    const stats = learningTracker.getLearningStats(daysInMonth);
    
    // 构建日历数据
    const data = new Map();
    stats.trends.dailyQuestions.forEach(dayData => {
      const date = new Date(dayData.date);
      if (date.getMonth() === month && date.getFullYear() === year) {
        data.set(date.getDate(), {
          questions: dayData.count,
          accuracy: stats.trends.dailyAccuracy.find(a => a.date === dayData.date)?.accuracy || 0,
          time: stats.trends.dailyTime.find(t => t.date === dayData.date)?.time || 0
        });
      }
    });
    
    setCalendarData(data);
    
    // 计算当月统计
    const monthlyStats = {
      totalQuestions: stats.totals.questions,
      averageAccuracy: stats.averages.accuracy,
      totalTime: stats.totals.timeSpent,
      activeDays: Array.from(data.values()).filter(d => d.questions > 0).length,
      currentStreak: stats.streaks.current.count,
      maxStreak: stats.streaks.max.count
    };
    
    setMonthStats(monthlyStats);
  };

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days = [];
    const today = new Date();
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const isCurrentMonth = date.getMonth() === month;
      const isToday = date.toDateString() === today.toDateString();
      const dayData = isCurrentMonth ? calendarData.get(date.getDate()) : null;
      
      days.push({
        date: date.getDate(),
        fullDate: new Date(date),
        isCurrentMonth,
        isToday,
        data: dayData
      });
    }
    
    return days;
  };

  const formatTime = (milliseconds) => {
    const minutes = Math.round(milliseconds / 1000 / 60);
    return `${minutes}分钟`;
  };

  const monthNames = [
    '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月'
  ];

  const dayNames = ['日', '一', '二', '三', '四', '五', '六'];

  if (!monthStats) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '2rem', color: 'white' }}>
          <div style={{ fontSize: '1.2rem' }}>加载日历数据中...</div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <Header>
        <Title>
          <CalendarIcon size={32} />
          学习日历
        </Title>
        <Subtitle>追踪您的每日学习习惯</Subtitle>
      </Header>

      <StatsSection>
        <StatCard color="#667eea">
          <StatHeader>
            <StatIcon color="#667eea">
              <Target size={20} />
            </StatIcon>
            <StatTitle>本月题目</StatTitle>
          </StatHeader>
          <StatValue>{monthStats.totalQuestions}</StatValue>
          <StatLabel>道题目</StatLabel>
        </StatCard>

        <StatCard color="#38a169">
          <StatHeader>
            <StatIcon color="#38a169">
              <TrendingUp size={20} />
            </StatIcon>
            <StatTitle>平均准确率</StatTitle>
          </StatHeader>
          <StatValue>{(monthStats.averageAccuracy * 100).toFixed(1)}%</StatValue>
          <StatLabel>本月表现</StatLabel>
        </StatCard>

        <StatCard color="#d69e2e">
          <StatHeader>
            <StatIcon color="#d69e2e">
              <Flame size={20} />
            </StatIcon>
            <StatTitle>当前连续</StatTitle>
          </StatHeader>
          <StatValue>{monthStats.currentStreak}</StatValue>
          <StatLabel>天连续学习</StatLabel>
        </StatCard>

        <StatCard color="#e53e3e">
          <StatHeader>
            <StatIcon color="#e53e3e">
              <Award size={20} />
            </StatIcon>
            <StatTitle>活跃天数</StatTitle>
          </StatHeader>
          <StatValue>{monthStats.activeDays}</StatValue>
          <StatLabel>本月学习天数</StatLabel>
        </StatCard>
      </StatsSection>

      <CalendarContainer>
        <CalendarHeader>
          <MonthNavigation>
            <NavButton onClick={() => navigateMonth(-1)}>
              <ChevronLeft size={20} />
            </NavButton>
            <MonthTitle>
              {currentDate.getFullYear()}年 {monthNames[currentDate.getMonth()]}
            </MonthTitle>
            <NavButton onClick={() => navigateMonth(1)}>
              <ChevronRight size={20} />
            </NavButton>
          </MonthNavigation>
        </CalendarHeader>

        <CalendarGrid>
          {dayNames.map(day => (
            <DayHeader key={day}>{day}</DayHeader>
          ))}
          
          {generateCalendarDays().map((day, index) => (
            <DayCell
              key={index}
              isToday={day.isToday}
              isOtherMonth={!day.isCurrentMonth}
            >
              <DayNumber>{day.date}</DayNumber>
              {day.data && day.data.questions > 0 && (
                <>
                  <ActivityCount>{day.data.questions}题</ActivityCount>
                  <ActivityIndicator count={day.data.questions} />
                </>
              )}
            </DayCell>
          ))}
        </CalendarGrid>
      </CalendarContainer>
    </PageContainer>
  );
};

export default CalendarPage;
