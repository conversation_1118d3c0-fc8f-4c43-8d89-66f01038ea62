{"name": "firebase", "version": "12.0.0", "description": "Firebase JavaScript library for web and Node.js", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "license": "Apache-2.0", "homepage": "https://firebase.google.com/", "keywords": ["authentication", "database", "Firebase", "firebase", "realtime", "storage", "performance", "remote-config"], "files": ["**/dist/**/*", "**/package.json", "/firebase*.js", "/firebase*.map", "compat/index.d.ts"], "exports": {"./analytics": {"types": "./analytics/dist/analytics/index.d.ts", "node": {"require": "./analytics/dist/index.cjs.js", "import": "./analytics/dist/index.mjs"}, "browser": {"require": "./analytics/dist/index.cjs.js", "import": "./analytics/dist/esm/index.esm.js"}, "default": "./analytics/dist/esm/index.esm.js"}, "./app": {"types": "./app/dist/app/index.d.ts", "node": {"require": "./app/dist/index.cjs.js", "import": "./app/dist/index.mjs"}, "browser": {"require": "./app/dist/index.cjs.js", "import": "./app/dist/esm/index.esm.js"}, "default": "./app/dist/esm/index.esm.js"}, "./app-check": {"types": "./app-check/dist/app-check/index.d.ts", "node": {"require": "./app-check/dist/index.cjs.js", "import": "./app-check/dist/index.mjs"}, "browser": {"require": "./app-check/dist/index.cjs.js", "import": "./app-check/dist/esm/index.esm.js"}, "default": "./app-check/dist/esm/index.esm.js"}, "./auth": {"types": "./auth/dist/auth/index.d.ts", "node": {"require": "./auth/dist/index.cjs.js", "import": "./auth/dist/index.mjs"}, "browser": {"require": "./auth/dist/index.cjs.js", "import": "./auth/dist/esm/index.esm.js"}, "default": "./auth/dist/esm/index.esm.js"}, "./auth/cordova": {"types": "./auth/cordova/dist/auth/cordova/index.d.ts", "node": {"require": "./auth/cordova/dist/index.cjs.js", "import": "./auth/cordova/dist/index.mjs"}, "browser": {"require": "./auth/cordova/dist/index.cjs.js", "import": "./auth/cordova/dist/esm/index.esm.js"}, "default": "./auth/cordova/dist/esm/index.esm.js"}, "./auth/web-extension": {"types": "./auth/web-extension/dist/auth/web-extension/index.d.ts", "node": {"require": "./auth/web-extension/dist/index.cjs.js", "import": "./auth/web-extension/dist/index.mjs"}, "browser": {"require": "./auth/web-extension/dist/index.cjs.js", "import": "./auth/web-extension/dist/esm/index.esm.js"}, "default": "./auth/web-extension/dist/esm/index.esm.js"}, "./database": {"types": "./database/dist/database/index.d.ts", "node": {"require": "./database/dist/index.cjs.js", "import": "./database/dist/index.mjs"}, "browser": {"require": "./database/dist/index.cjs.js", "import": "./database/dist/esm/index.esm.js"}, "default": "./database/dist/esm/index.esm.js"}, "./data-connect": {"types": "./data-connect/dist/data-connect/index.d.ts", "node": {"require": "./data-connect/dist/index.cjs.js", "import": "./data-connect/dist/index.mjs"}, "browser": {"require": "./data-connect/dist/index.cjs.js", "import": "./data-connect/dist/esm/index.esm.js"}, "default": "./data-connect/dist/esm/index.esm.js"}, "./firestore": {"types": "./firestore/dist/firestore/index.d.ts", "node": {"require": "./firestore/dist/index.cjs.js", "import": "./firestore/dist/index.mjs"}, "browser": {"require": "./firestore/dist/index.cjs.js", "import": "./firestore/dist/esm/index.esm.js"}, "default": "./firestore/dist/esm/index.esm.js"}, "./firestore/lite": {"types": "./firestore/lite/dist/firestore/lite/index.d.ts", "node": {"require": "./firestore/lite/dist/index.cjs.js", "import": "./firestore/lite/dist/index.mjs"}, "browser": {"require": "./firestore/lite/dist/index.cjs.js", "import": "./firestore/lite/dist/esm/index.esm.js"}, "default": "./firestore/lite/dist/esm/index.esm.js"}, "./functions": {"types": "./functions/dist/functions/index.d.ts", "node": {"require": "./functions/dist/index.cjs.js", "import": "./functions/dist/index.mjs"}, "browser": {"require": "./functions/dist/index.cjs.js", "import": "./functions/dist/esm/index.esm.js"}, "default": "./functions/dist/esm/index.esm.js"}, "./installations": {"types": "./installations/dist/installations/index.d.ts", "node": {"require": "./installations/dist/index.cjs.js", "import": "./installations/dist/index.mjs"}, "browser": {"require": "./installations/dist/index.cjs.js", "import": "./installations/dist/esm/index.esm.js"}, "default": "./installations/dist/esm/index.esm.js"}, "./messaging": {"types": "./messaging/dist/messaging/index.d.ts", "node": {"require": "./messaging/dist/index.cjs.js", "import": "./messaging/dist/index.mjs"}, "browser": {"require": "./messaging/dist/index.cjs.js", "import": "./messaging/dist/esm/index.esm.js"}, "default": "./messaging/dist/esm/index.esm.js"}, "./messaging/sw": {"types": "./messaging/sw/dist/messaging/sw/index.d.ts", "node": {"require": "./messaging/sw/dist/index.cjs.js", "import": "./messaging/sw/dist/index.mjs"}, "browser": {"require": "./messaging/sw/dist/index.cjs.js", "import": "./messaging/sw/dist/esm/index.esm.js"}, "default": "./messaging/sw/dist/esm/index.esm.js"}, "./performance": {"types": "./performance/dist/performance/index.d.ts", "node": {"require": "./performance/dist/index.cjs.js", "import": "./performance/dist/index.mjs"}, "browser": {"require": "./performance/dist/index.cjs.js", "import": "./performance/dist/esm/index.esm.js"}, "default": "./performance/dist/esm/index.esm.js"}, "./remote-config": {"types": "./remote-config/dist/remote-config/index.d.ts", "node": {"require": "./remote-config/dist/index.cjs.js", "import": "./remote-config/dist/index.mjs"}, "browser": {"require": "./remote-config/dist/index.cjs.js", "import": "./remote-config/dist/esm/index.esm.js"}, "default": "./remote-config/dist/esm/index.esm.js"}, "./storage": {"types": "./storage/dist/storage/index.d.ts", "node": {"require": "./storage/dist/index.cjs.js", "import": "./storage/dist/index.mjs"}, "browser": {"require": "./storage/dist/index.cjs.js", "import": "./storage/dist/esm/index.esm.js"}, "default": "./storage/dist/esm/index.esm.js"}, "./ai": {"types": "./ai/dist/ai/index.d.ts", "node": {"require": "./ai/dist/index.cjs.js", "import": "./ai/dist/index.mjs"}, "browser": {"require": "./ai/dist/index.cjs.js", "import": "./ai/dist/esm/index.esm.js"}, "default": "./ai/dist/esm/index.esm.js"}, "./compat/analytics": {"types": "./compat/analytics/dist/compat/analytics/index.d.ts", "node": {"require": "./compat/analytics/dist/index.cjs.js", "import": "./compat/analytics/dist/index.mjs"}, "browser": {"require": "./compat/analytics/dist/index.cjs.js", "import": "./compat/analytics/dist/esm/index.esm.js"}, "default": "./compat/analytics/dist/esm/index.esm.js"}, "./compat/app": {"types": "./compat/index.d.ts", "node": {"require": "./compat/app/dist/index.cjs.js", "import": "./compat/app/dist/index.mjs"}, "browser": {"require": "./compat/app/dist/index.cjs.js", "import": "./compat/app/dist/esm/index.esm.js"}, "default": "./compat/app/dist/esm/index.esm.js"}, "./compat/app-check": {"types": "./compat/app-check/dist/compat/app-check/index.d.ts", "node": {"require": "./compat/app-check/dist/index.cjs.js", "import": "./compat/app-check/dist/index.mjs"}, "browser": {"require": "./compat/app-check/dist/index.cjs.js", "import": "./compat/app-check/dist/esm/index.esm.js"}, "default": "./compat/app-check/dist/esm/index.esm.js"}, "./compat/auth": {"types": "./compat/auth/dist/compat/auth/index.d.ts", "node": {"require": "./compat/auth/dist/index.cjs.js", "import": "./compat/auth/dist/index.mjs"}, "browser": {"require": "./compat/auth/dist/index.cjs.js", "import": "./compat/auth/dist/esm/index.esm.js"}, "default": "./compat/auth/dist/esm/index.esm.js"}, "./compat/database": {"types": "./compat/database/dist/compat/database/index.d.ts", "node": {"require": "./compat/database/dist/index.cjs.js", "import": "./compat/database/dist/index.mjs"}, "browser": {"require": "./compat/database/dist/index.cjs.js", "import": "./compat/database/dist/esm/index.esm.js"}, "default": "./compat/database/dist/esm/index.esm.js"}, "./compat/firestore": {"types": "./compat/firestore/dist/compat/firestore/index.d.ts", "node": {"require": "./compat/firestore/dist/index.cjs.js", "import": "./compat/firestore/dist/index.mjs"}, "browser": {"require": "./compat/firestore/dist/index.cjs.js", "import": "./compat/firestore/dist/esm/index.esm.js"}, "default": "./compat/firestore/dist/esm/index.esm.js"}, "./compat/functions": {"types": "./compat/functions/dist/compat/functions/index.d.ts", "node": {"require": "./compat/functions/dist/index.cjs.js", "import": "./compat/functions/dist/index.mjs"}, "browser": {"require": "./compat/functions/dist/index.cjs.js", "import": "./compat/functions/dist/esm/index.esm.js"}, "default": "./compat/functions/dist/esm/index.esm.js"}, "./compat/installations": {"types": "./compat/installations/dist/compat/installations/index.d.ts", "node": {"require": "./compat/installations/dist/index.cjs.js", "import": "./compat/installations/dist/index.mjs"}, "browser": {"require": "./compat/installations/dist/index.cjs.js", "import": "./compat/installations/dist/esm/index.esm.js"}, "default": "./compat/installations/dist/esm/index.esm.js"}, "./compat/messaging": {"types": "./compat/messaging/dist/compat/messaging/index.d.ts", "node": {"require": "./compat/messaging/dist/index.cjs.js", "import": "./compat/messaging/dist/index.mjs"}, "browser": {"require": "./compat/messaging/dist/index.cjs.js", "import": "./compat/messaging/dist/esm/index.esm.js"}, "default": "./compat/messaging/dist/esm/index.esm.js"}, "./compat/performance": {"types": "./compat/performance/dist/compat/performance/index.d.ts", "node": {"require": "./compat/performance/dist/index.cjs.js", "import": "./compat/performance/dist/index.mjs"}, "browser": {"require": "./compat/performance/dist/index.cjs.js", "import": "./compat/performance/dist/esm/index.esm.js"}, "default": "./compat/performance/dist/esm/index.esm.js"}, "./compat/remote-config": {"types": "./compat/remote-config/dist/compat/remote-config/index.d.ts", "node": {"require": "./compat/remote-config/dist/index.cjs.js", "import": "./compat/remote-config/dist/index.mjs"}, "browser": {"require": "./compat/remote-config/dist/index.cjs.js", "import": "./compat/remote-config/dist/esm/index.esm.js"}, "default": "./compat/remote-config/dist/esm/index.esm.js"}, "./compat/storage": {"types": "./compat/storage/dist/compat/storage/index.d.ts", "node": {"require": "./compat/storage/dist/index.cjs.js", "import": "./compat/storage/dist/index.mjs"}, "browser": {"require": "./compat/storage/dist/index.cjs.js", "import": "./compat/storage/dist/esm/index.esm.js"}, "default": "./compat/storage/dist/esm/index.esm.js"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "scripts": {"build": "rollup -c && gulp cdn-type-module-path && yarn build:compat", "build:internal": "rollup -c && gulp cdn-type-module-path-internal && yarn build:compat", "build:compat": "rollup -c compat/rollup.config.js", "dev": "rollup -c -w", "test": "echo 'No test suite for firebase wrapper'", "test:ci": "echo 'No test suite for firebase wrapper'", "trusted-type-check": "tsec -p tsconfig.json --noEmit"}, "dependencies": {"@firebase/ai": "2.0.0", "@firebase/app": "0.14.0", "@firebase/app-compat": "0.5.0", "@firebase/app-types": "0.9.3", "@firebase/auth": "1.11.0", "@firebase/auth-compat": "0.6.0", "@firebase/data-connect": "0.3.11", "@firebase/database": "1.1.0", "@firebase/database-compat": "2.1.0", "@firebase/firestore": "4.9.0", "@firebase/firestore-compat": "0.4.0", "@firebase/functions": "0.13.0", "@firebase/functions-compat": "0.4.0", "@firebase/installations": "0.6.19", "@firebase/installations-compat": "0.2.19", "@firebase/messaging": "0.12.23", "@firebase/messaging-compat": "0.2.23", "@firebase/storage": "0.14.0", "@firebase/storage-compat": "0.4.0", "@firebase/performance": "0.7.8", "@firebase/performance-compat": "0.2.21", "@firebase/remote-config": "0.6.6", "@firebase/remote-config-compat": "0.2.19", "@firebase/analytics": "0.10.18", "@firebase/analytics-compat": "0.2.24", "@firebase/app-check": "0.11.0", "@firebase/app-check-compat": "0.4.0", "@firebase/util": "1.13.0"}, "devDependencies": {"rollup": "2.79.2", "@rollup/plugin-commonjs": "21.1.0", "@rollup/plugin-node-resolve": "16.0.0", "rollup-plugin-sourcemaps": "0.6.3", "@rollup/plugin-terser": "0.4.4", "rollup-plugin-typescript2": "0.36.0", "rollup-plugin-uglify": "6.0.4", "gulp": "4.0.2", "gulp-sourcemaps": "3.0.0", "gulp-replace": "1.1.4", "typescript": "5.5.4", "rollup-plugin-license": "3.5.3"}, "components": ["ai", "analytics", "app", "app-check", "auth", "auth/cordova", "auth/web-extension", "functions", "firestore", "firestore/lite", "installations", "storage", "performance", "remote-config", "messaging", "messaging/sw", "database", "data-connect"], "typings": "empty.d.ts"}